@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Cyberpunk BONKAT Design System */
    --background: 220 15% 8%;
    --foreground: 120 100% 85%;

    --card: 220 20% 12%;
    --card-foreground: 120 100% 90%;

    --popover: 220 25% 10%;
    --popover-foreground: 120 100% 90%;

    --primary: 120 100% 50%;
    --primary-foreground: 220 20% 8%;

    --secondary: 25 100% 60%;
    --secondary-foreground: 220 20% 8%;

    --muted: 220 15% 15%;
    --muted-foreground: 120 30% 70%;

    --accent: 200 100% 60%;
    --accent-foreground: 220 20% 8%;

    --destructive: 0 100% 60%;
    --destructive-foreground: 220 20% 8%;

    --border: 120 30% 25%;
    --input: 220 20% 15%;
    --ring: 120 100% 50%;

    /* Custom BONKAT Colors */
    --neon-green: 120 100% 50%;
    --neon-blue: 200 100% 60%;
    --hot-orange: 25 100% 60%;
    --crypto-gold: 45 100% 60%;
    --matrix-black: 220 20% 5%;
    --glow-cyan: 180 100% 70%;

    /* Gradients */
    --gradient-matrix: linear-gradient(135deg, hsl(var(--matrix-black)), hsl(220 30% 10%));
    --gradient-neon: linear-gradient(45deg, hsl(var(--neon-green)), hsl(var(--neon-blue)));
    --gradient-fire: linear-gradient(135deg, hsl(var(--hot-orange)), hsl(0 100% 70%));
    --gradient-cyber: linear-gradient(90deg, hsl(var(--neon-green) / 0.1), hsl(var(--neon-blue) / 0.1));

    /* Shadows & Glows */
    --shadow-neon: 0 0 20px hsl(var(--neon-green) / 0.5);
    --shadow-blue: 0 0 30px hsl(var(--neon-blue) / 0.4);
    --shadow-orange: 0 0 25px hsl(var(--hot-orange) / 0.6);
    --glow-matrix: 0 0 40px hsl(var(--neon-green) / 0.3);

    /* Animations */
    --transition-cyber: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-glow: all 0.4s ease-out;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}